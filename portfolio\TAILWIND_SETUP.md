# Manual Tailwind CSS Configuration Guide

This portfolio has been set up with Tailwind CSS implementation but **without** configuration files, allowing you to configure Tailwind CSS manually according to your preferences.

## 🚀 Current Setup

The portfolio currently uses:
- **Tailwind CSS v4** with `@tailwindcss/postcss` plugin
- Basic PostCSS configuration
- Standard Tailwind utility classes
- Default color scheme (blue/purple gradients)

## 📋 What's Already Configured

### Files Present:
- ✅ `postcss.config.js` - Basic PostCSS configuration
- ✅ `src/index.css` - Tailwind import and custom styles
- ✅ All components use standard Tailwind classes

### Files Removed (for manual configuration):
- ❌ `tailwind.config.js` - You need to create this
- ❌ Custom color schemes - Using default Tailwind colors

## 🛠️ Manual Configuration Steps

### Step 1: Create Tailwind Configuration

Create `tailwind.config.js` in the root directory:

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        // Add your custom colors here
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        dark: {
          100: '#1e1e1e',
          200: '#2d2d2d',
          300: '#404040',
          400: '#525252',
          500: '#737373',
        }
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Poppins', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'bounce-slow': 'bounce 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
```

### Step 2: Update Component Colors (Optional)

If you want to use custom colors, update these classes in components:

**Current classes to replace:**
- `bg-blue-600` → `bg-primary-600`
- `text-blue-600` → `text-primary-600`
- `hover:bg-blue-700` → `hover:bg-primary-700`
- `border-blue-600` → `border-primary-600`

**Files to update:**
- `src/components/Navbar.jsx`
- `src/components/Hero.jsx`
- `src/components/About.jsx`
- `src/components/Services.jsx`
- `src/components/Skills.jsx`
- `src/components/Projects.jsx`
- `src/components/Contact.jsx`
- `src/components/Footer.jsx`
- `src/index.css`

### Step 3: Dark Mode Configuration

The portfolio includes dark mode support. To customize dark mode colors:

1. **Add dark mode colors to your config:**
```javascript
colors: {
  dark: {
    100: '#your-dark-bg',
    200: '#your-darker-bg',
    300: '#your-darkest-bg',
  }
}
```

2. **Update dark mode classes in components:**
- `dark:bg-gray-900` → `dark:bg-dark-100`
- `dark:text-gray-300` → `dark:text-dark-500`

## 🎨 Customization Examples

### Custom Color Scheme
```javascript
// tailwind.config.js
colors: {
  brand: {
    50: '#f0f9ff',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
  }
}
```

### Custom Fonts
```javascript
// tailwind.config.js
fontFamily: {
  'heading': ['Your Custom Font', 'sans-serif'],
  'body': ['Another Font', 'sans-serif'],
}
```

### Custom Animations
```javascript
// tailwind.config.js
animation: {
  'custom-bounce': 'bounce 1s infinite',
  'fade-in-up': 'fadeInUp 0.6s ease-out',
}
```

## 🔧 Advanced Configuration

### Adding Plugins
```bash
npm install @tailwindcss/typography @tailwindcss/forms
```

```javascript
// tailwind.config.js
plugins: [
  require('@tailwindcss/typography'),
  require('@tailwindcss/forms'),
],
```

### Custom Utilities
```javascript
// tailwind.config.js
theme: {
  extend: {
    utilities: {
      '.text-shadow': {
        'text-shadow': '2px 2px 4px rgba(0, 0, 0, 0.1)',
      },
    }
  }
}
```

## 🚀 Quick Start Commands

```bash
# Install additional Tailwind plugins (optional)
npm install @tailwindcss/typography @tailwindcss/forms @tailwindcss/aspect-ratio

# Start development server
npm run dev

# Build for production
npm run build
```

## 📝 Current Component Structure

The portfolio uses these Tailwind patterns:

### Layout Classes:
- `container-custom` - Max width container
- `section-padding` - Consistent section spacing

### Component Classes:
- `btn-primary` - Primary button styles
- `btn-secondary` - Secondary button styles
- `gradient-text` - Gradient text effect
- `card-hover` - Card hover animations

### Utility Classes:
- `text-shadow` - Text shadow effect
- `glass-effect` - Glassmorphism effect

## 🎯 Next Steps

1. **Create your `tailwind.config.js`** with your preferred settings
2. **Customize colors** to match your brand
3. **Update component classes** if using custom color names
4. **Test responsive design** on different screen sizes
5. **Optimize for production** with purging unused styles

## 📞 Support

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Tailwind CSS v4 Migration Guide](https://tailwindcss.com/docs/v4-beta)
- [PostCSS Configuration](https://postcss.org/)

---

🎉 **Ready to customize!** Your portfolio is set up with Tailwind CSS and ready for your personal configuration.

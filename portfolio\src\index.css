@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }

  body {
    @apply bg-white dark:bg-dark-100 text-gray-900 dark:text-white;
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-300;
  }

  .section-padding {
    @apply py-20 px-4 sm:px-6 lg:px-8;
  }

  .container-custom {
    @apply max-w-7xl mx-auto;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }

  .card-hover {
    @apply transition-all duration-300 transform hover:scale-105 hover:shadow-xl;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-dark-200;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-500 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-600;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@tailwindcss/vite": "^4.1.10", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "lucide-react": "^0.515.0", "maath": "^0.10.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.10", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "vite": "^6.3.5"}}
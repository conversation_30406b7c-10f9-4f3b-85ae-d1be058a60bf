# Portfolio Customization Guide

This guide will help you customize your interactive portfolio with your personal information and preferences.

## 🎯 Quick Start Customization

### 1. Personal Information

#### Hero Section (`src/components/Hero.jsx`)
- **Line 95**: Replace `"Your Name"` with your actual name
- **Line 101**: Update the subtitle/role
- **Line 107**: Modify the description paragraph
- **Lines 130-133**: Update social media links

#### About Section (`src/components/About.jsx`)
- **Lines 50-52**: Replace placeholder initials "YN" with your initials
- **Lines 67-85**: Update the about me paragraphs with your story
- **Lines 40-47**: Modify the highlights to match your expertise

#### Contact Section (`src/components/Contact.jsx`)
- **Lines 35-47**: Update contact information (email, phone, location)
- **Line 36**: Replace with your actual email
- **Line 41**: Replace with your actual phone number
- **Line 46**: Replace with your actual location

#### Footer (`src/components/Footer.jsx`)
- **Lines 18-21**: Update social media links
- **Lines 67-81**: Update contact information
- **Line 95**: Replace "Your Name" with your actual name

### 2. Projects Section (`src/components/Projects.jsx`)

Update the projects array (lines 25-55) with your actual projects:

```javascript
const projects = [
  {
    title: 'Your Project Name',
    description: 'Your project description...',
    technologies: ['React', 'Node.js', 'MongoDB'], // Your tech stack
    liveLink: 'https://your-project-url.com',
    githubLink: 'https://github.com/yourusername/project',
    icon: Award, // Choose appropriate icon
    category: 'Web Development',
    featured: true
  },
  // Add more projects...
]
```

### 3. Skills Section (`src/components/Skills.jsx`)

#### Technical Skills (lines 25-32)
Update with your actual skills and proficiency levels:
```javascript
const technicalSkills = [
  { name: 'Your Skill', percentage: 85, color: 'bg-blue-500' },
  // Add more skills...
]
```

#### Professional Skills (lines 34-39)
Update with your soft skills:
```javascript
const professionalSkills = [
  { name: 'Your Skill', percentage: 80, color: 'text-purple-500' },
  // Add more skills...
]
```

### 4. Services Section (`src/components/Services.jsx`)

Update the services array (lines 25-70) with your actual services:
```javascript
const services = [
  {
    icon: YourIcon,
    title: 'Your Service',
    description: 'Your service description...',
    features: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4'],
    color: 'from-blue-500 to-cyan-500'
  },
  // Add more services...
]
```

## 🎨 Visual Customization

### Colors (`tailwind.config.js`)

Update the color scheme by modifying the primary colors:
```javascript
colors: {
  primary: {
    50: '#your-color',
    100: '#your-color',
    // ... continue with your color palette
  }
}
```

### Fonts (`src/index.css`)

Change fonts by updating the Google Fonts import and font families:
```css
@import url('https://fonts.googleapis.com/css2?family=YourFont:wght@300;400;500;600;700&display=swap');
```

### Animations

Modify animation durations and effects in:
- `tailwind.config.js` - Custom animations
- Individual components - GSAP animations
- `src/index.css` - CSS animations

## 📱 Content Updates

### Meta Tags (`index.html`)
- Update title, description, and keywords
- Add your name as the author

### Social Media Links
Update all social media links throughout the components:
- Facebook: Replace `#` with your Facebook profile
- Instagram: Replace `#` with your Instagram profile  
- LinkedIn: Replace `#` with your LinkedIn profile
- GitHub: Replace `#` with your GitHub profile

### Resume/CV Download
Add your resume file to the `public` folder and update the download link in `Hero.jsx`.

## 🖼️ Images

### Profile Photo
Replace the placeholder in `About.jsx` with your actual photo:
1. Add your photo to `public/images/`
2. Update the image source in the About component

### Project Images
1. Add project screenshots to `public/images/projects/`
2. Update image sources in the Projects component

### Favicon
Replace `public/vite.svg` with your custom favicon.

## 🚀 Deployment Preparation

### Environment Variables
Create a `.env` file for any API keys or configuration:
```
VITE_EMAIL_SERVICE_ID=your_service_id
VITE_CONTACT_FORM_ENDPOINT=your_endpoint
```

### Build Optimization
Before deployment:
```bash
npm run build
```

### SEO Optimization
- Update meta tags in `index.html`
- Add structured data for better search engine visibility
- Optimize images for web (WebP format recommended)

## 🔧 Advanced Customization

### Adding New Sections
1. Create a new component in `src/components/`
2. Import and add it to `App.jsx`
3. Add navigation link in `Navbar.jsx`

### Custom Animations
- Use GSAP for complex animations
- Framer Motion for React-specific animations
- CSS animations for simple effects

### Theme Customization
The portfolio includes a dark/light theme toggle. Customize colors in:
- `tailwind.config.js` - Dark mode colors
- `src/index.css` - Custom dark mode styles

## 📞 Support

If you need help with customization:
1. Check the component comments for guidance
2. Refer to the documentation of used libraries:
   - [Tailwind CSS](https://tailwindcss.com/docs)
   - [GSAP](https://greensock.com/docs/)
   - [Framer Motion](https://www.framer.com/motion/)
   - [Three.js](https://threejs.org/docs/)

## ✅ Checklist

Before going live, make sure you've updated:
- [ ] Personal name and title
- [ ] About me content
- [ ] Contact information
- [ ] Social media links
- [ ] Projects with actual data
- [ ] Skills and percentages
- [ ] Services offered
- [ ] Profile photo
- [ ] Project images
- [ ] Resume/CV file
- [ ] Meta tags and SEO
- [ ] Favicon
- [ ] Color scheme (optional)
- [ ] Fonts (optional)

---

🎉 **Congratulations!** Your portfolio is ready to showcase your amazing work!
